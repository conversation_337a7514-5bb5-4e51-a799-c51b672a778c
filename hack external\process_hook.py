#!/usr/bin/env python3
"""
Process hooking utilities for finding and attaching to target applications
"""

import psutil
import pymem
import time
import subprocess
import re
from typing import Optional, List, Tuple
from config import TARGET_PROCESSES, TARGET_APPS


class ProcessHook:
    def __init__(self):
        self.target_process = None
        self.target_pid = None
        self.security_level = None

    def find_target_processes(self) -> List[Tuple[int, str, str]]:
        """
        Find all potential target processes
        Returns list of (pid, process_name, window_title) tuples
        """
        targets = []

        # Get all Python processes
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] in TARGET_PROCESSES:
                    # Get window title if available
                    window_title = self.get_window_title_by_pid(proc.info['pid'])
                    if window_title:
                        targets.append((proc.info['pid'], proc.info['name'], window_title))
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return targets

    def get_window_title_by_pid(self, pid: int) -> Optional[str]:
        """Get window title for a process ID using tasklist command"""
        try:
            # Use tasklist to get window titles
            result = subprocess.run(
                ['tasklist', '/fi', f'PID eq {pid}', '/fo', 'csv', '/v'],
                capture_output=True, text=True, timeout=5
            )

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:  # Skip header
                    # Parse CSV output to get window title
                    data_line = lines[1]
                    # Window title is typically the last field
                    fields = data_line.split('","')
                    if len(fields) > 8:
                        title = fields[-1].strip('"')
                        if title and title != "N/A":
                            return title
        except Exception:
            pass

        # Fallback: check if it's a Python process with our target apps
        try:
            proc = psutil.Process(pid)
            cmdline = proc.cmdline()
            if cmdline and len(cmdline) > 1:
                script_path = cmdline[-1]
                if 'main.py' in script_path:
                    if 'simple' in script_path:
                        return TARGET_APPS['simple']
                    elif 'medium' in script_path:
                        return TARGET_APPS['medium']
                    elif 'hack' in script_path:
                        return TARGET_APPS['hack']
        except:
            pass

        return None

    def identify_security_level(self, window_title: str) -> Optional[str]:
        """Identify security level based on window title"""
        for level, title in TARGET_APPS.items():
            if title in window_title:
                return level
        return None

    def attach_to_process(self, pid: int) -> bool:
        """Attach to a target process"""
        try:
            self.target_process = pymem.Pymem()
            self.target_process.open_process_from_id(pid)
            self.target_pid = pid
            return True
        except Exception as e:
            print(f"Failed to attach to process {pid}: {e}")
            return False

    def find_and_attach_target(self) -> Tuple[bool, Optional[str]]:
        """
        Find and attach to the first available target application
        Returns (success, security_level)
        """
        targets = self.find_target_processes()

        if not targets:
            return False, None

        print("Found target processes:")
        for i, (pid, name, title) in enumerate(targets):
            security_level = self.identify_security_level(title)
            print(f"{i+1}. PID: {pid}, Name: {name}, Title: {title}, Level: {security_level}")

        # Try to attach to each target
        for pid, name, title in targets:
            security_level = self.identify_security_level(title)
            if security_level and self.attach_to_process(pid):
                self.security_level = security_level
                print(f"Successfully attached to {title} (PID: {pid})")
                return True, security_level

        return False, None

    def is_process_alive(self) -> bool:
        """Check if the target process is still running"""
        if not self.target_pid:
            return False

        try:
            proc = psutil.Process(self.target_pid)
            return proc.is_running()
        except psutil.NoSuchProcess:
            return False

    def detach(self):
        """Detach from the target process"""
        if self.target_process:
            try:
                self.target_process.close_handle()
            except:
                pass
            self.target_process = None
            self.target_pid = None
            self.security_level = None

    def wait_for_target(self, timeout: float = 30.0) -> Tuple[bool, Optional[str]]:
        """
        Wait for a target application to appear
        Returns (success, security_level)
        """
        print(f"Waiting for target application (timeout: {timeout}s)...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            success, security_level = self.find_and_attach_target()
            if success:
                return True, security_level
            time.sleep(1.0)

        return False, None
