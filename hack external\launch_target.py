#!/usr/bin/env python3
"""
Target Application Launcher
Starts the simple counter application for testing the exploit
"""

import subprocess
import sys
import os
import time
from colorama import init, Fore, Style

init()

def print_status(message: str, status_type: str = "info"):
    """Print colored status message"""
    colors = {
        "info": Fore.CYAN,
        "success": Fore.GREEN,
        "warning": Fore.YELLOW,
        "error": Fore.RED
    }
    color = colors.get(status_type, Fore.WHITE)
    print(f"{color}[{status_type.upper()}]{Style.RESET_ALL} {message}")

def main():
    print(f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    TARGET APP LAUNCHER                      ║
║              Starting Simple Counter Application            ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}

""")
    
    # Check if target directory exists
    target_dir = "../simple test external hack"
    if not os.path.exists(target_dir):
        print_status("Target directory not found", "error")
        print_status(f"Looking for: {os.path.abspath(target_dir)}", "error")
        return False
    
    # Check if main.py exists
    target_file = os.path.join(target_dir, "main.py")
    if not os.path.exists(target_file):
        print_status("Target main.py not found", "error")
        return False
    
    print_status("Starting simple counter application...", "info")
    print_status("This will open a pygame window with a clickable button", "info")
    print_status("Leave this window open and run the exploit in another terminal", "warning")
    print()
    
    try:
        # Start the target application
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            cwd=target_dir,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        
        print_status(f"Target application started (PID: {process.pid})", "success")
        print_status("You can now run the exploit:", "info")
        print_status("python working_exploit.py", "info")
        print()
        print_status("Press Ctrl+C to stop the target application", "warning")
        
        # Wait for the process
        process.wait()
        
    except KeyboardInterrupt:
        print_status("Stopping target application...", "info")
        try:
            process.terminate()
        except:
            pass
    except Exception as e:
        print_status(f"Error starting target: {e}", "error")
        return False
    
    return True

if __name__ == "__main__":
    main()
