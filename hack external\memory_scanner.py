#!/usr/bin/env python3
"""
Memory scanning utilities for finding and modifying target application memory
"""

import pymem
import struct
import time
from typing import List, Tu<PERSON>, Optional
from config import MEMORY_PATTERNS


class MemoryScanner:
    def __init__(self, process_handle: pymem.Pymem):
        self.process = process_handle
        self.found_addresses = {}
        
    def scan_for_pattern(self, pattern: bytes, start_address: int = None, 
                        end_address: int = None) -> List[int]:
        """
        Scan process memory for a specific byte pattern
        Returns list of addresses where pattern was found
        """
        addresses = []
        
        try:
            # Get memory regions
            for region in self.process.list_modules():
                if start_address and region.lpBaseOfDll < start_address:
                    continue
                if end_address and region.lpBaseOfDll > end_address:
                    continue
                    
                try:
                    # Read memory region
                    memory_data = self.process.read_bytes(
                        region.lpBaseOfDll, 
                        region.SizeOfImage
                    )
                    
                    # Search for pattern
                    offset = 0
                    while True:
                        pos = memory_data.find(pattern, offset)
                        if pos == -1:
                            break
                        addresses.append(region.lpBaseOfDll + pos)
                        offset = pos + 1
                        
                except (pymem.exception.MemoryReadError, 
                       pymem.exception.WinAPIError):
                    continue
                    
        except Exception as e:
            print(f"Error scanning memory: {e}")
            
        return addresses
    
    def find_counter_addresses(self, security_level: str) -> List[int]:
        """
        Find potential counter variable addresses based on security level
        """
        if security_level not in MEMORY_PATTERNS:
            return []
            
        patterns = MEMORY_PATTERNS[security_level]["counter_patterns"]
        all_addresses = []
        
        for pattern in patterns:
            addresses = self.scan_for_pattern(pattern)
            all_addresses.extend(addresses)
            
        # Remove duplicates and sort
        return sorted(list(set(all_addresses)))
    
    def read_int32(self, address: int) -> Optional[int]:
        """Read a 32-bit integer from memory address"""
        try:
            data = self.process.read_bytes(address, 4)
            return struct.unpack('<I', data)[0]
        except:
            return None
    
    def write_int32(self, address: int, value: int) -> bool:
        """Write a 32-bit integer to memory address"""
        try:
            data = struct.pack('<I', value)
            self.process.write_bytes(address, data, 4)
            return True
        except:
            return False
    
    def monitor_addresses(self, addresses: List[int], duration: float = 5.0) -> dict:
        """
        Monitor memory addresses for changes over a duration
        Returns dict of address -> list of values seen
        """
        monitoring_data = {addr: [] for addr in addresses}
        start_time = time.time()
        
        while time.time() - start_time < duration:
            for addr in addresses:
                value = self.read_int32(addr)
                if value is not None:
                    monitoring_data[addr].append((time.time(), value))
            time.sleep(0.1)
            
        return monitoring_data
    
    def find_dynamic_counter(self, security_level: str) -> Optional[int]:
        """
        Dynamically find the counter by monitoring memory changes
        """
        print(f"Scanning for {security_level} counter addresses...")
        
        # Get initial addresses
        addresses = self.find_counter_addresses(security_level)
        print(f"Found {len(addresses)} potential addresses")
        
        if not addresses:
            return None
            
        # Monitor for changes
        print("Monitoring addresses for changes (click the target app button)...")
        monitoring_data = self.monitor_addresses(addresses, 10.0)
        
        # Find addresses that changed
        changing_addresses = []
        for addr, values in monitoring_data.items():
            if len(set(v[1] for v in values)) > 1:  # Value changed
                changing_addresses.append(addr)
                
        print(f"Found {len(changing_addresses)} changing addresses")
        
        # Return the most likely counter address
        if changing_addresses:
            return changing_addresses[0]
            
        return None
