#!/usr/bin/env python3
"""
Memory scanning utilities for finding and modifying target application memory
"""

import pymem
import struct
import time
from typing import List, Tu<PERSON>, Optional
from config import MEMORY_PATTERNS


class MemoryScanner:
    def __init__(self, process_handle: pymem.Pymem):
        self.process = process_handle
        self.found_addresses = {}

    def scan_for_pattern(self, pattern: bytes, start_address: int = None,
                        end_address: int = None) -> List[int]:
        """
        Scan process memory for a specific byte pattern
        Returns list of addresses where pattern was found
        """
        addresses = []

        try:
            # Get all memory regions, not just modules
            memory_info = self.process.memory_regions()

            for region in memory_info:
                # Skip if region is not readable
                if not (region.Protect & 0x04):  # PAGE_READWRITE
                    continue

                if start_address and region.BaseAddress < start_address:
                    continue
                if end_address and region.BaseAddress > end_address:
                    continue

                try:
                    # Read memory region in smaller chunks to avoid errors
                    chunk_size = min(region.RegionSize, 1024 * 1024)  # 1MB chunks

                    for offset in range(0, region.RegionSize, chunk_size):
                        try:
                            current_address = region.BaseAddress + offset
                            read_size = min(chunk_size, region.RegionSize - offset)

                            memory_data = self.process.read_bytes(current_address, read_size)

                            # Search for pattern
                            search_offset = 0
                            while True:
                                pos = memory_data.find(pattern, search_offset)
                                if pos == -1:
                                    break
                                addresses.append(current_address + pos)
                                search_offset = pos + 1

                        except (pymem.exception.MemoryReadError,
                               pymem.exception.WinAPIError):
                            continue

                except Exception:
                    continue

        except Exception as e:
            print(f"Error scanning memory: {e}")

        return addresses

    def find_counter_addresses(self, security_level: str) -> List[int]:
        """
        Find potential counter variable addresses based on security level
        """
        if security_level not in MEMORY_PATTERNS:
            return []

        patterns = MEMORY_PATTERNS[security_level]["counter_patterns"]
        all_addresses = []

        for pattern in patterns:
            addresses = self.scan_for_pattern(pattern)
            all_addresses.extend(addresses)

        # Remove duplicates and sort
        return sorted(list(set(all_addresses)))

    def read_int32(self, address: int) -> Optional[int]:
        """Read a 32-bit integer from memory address"""
        try:
            data = self.process.read_bytes(address, 4)
            return struct.unpack('<I', data)[0]
        except:
            return None

    def write_int32(self, address: int, value: int) -> bool:
        """Write a 32-bit integer to memory address"""
        try:
            data = struct.pack('<I', value)
            self.process.write_bytes(address, data, 4)
            return True
        except:
            return False

    def monitor_addresses(self, addresses: List[int], duration: float = 5.0) -> dict:
        """
        Monitor memory addresses for changes over a duration
        Returns dict of address -> list of values seen
        """
        monitoring_data = {addr: [] for addr in addresses}
        start_time = time.time()

        while time.time() - start_time < duration:
            for addr in addresses:
                value = self.read_int32(addr)
                if value is not None:
                    monitoring_data[addr].append((time.time(), value))
            time.sleep(0.1)

        return monitoring_data

    def find_dynamic_counter(self, security_level: str) -> Optional[int]:
        """
        Dynamically find the counter by monitoring memory changes
        """
        print(f"Scanning for {security_level} counter addresses...")

        # Use a simpler approach - scan for small integer values
        addresses = self.scan_for_small_integers()
        print(f"Found {len(addresses)} potential integer addresses")

        if not addresses:
            return None

        # Monitor for changes with shorter timeout
        print("Monitoring addresses for changes (click the target app button)...")
        monitoring_data = self.monitor_addresses(addresses[:1000], 5.0)  # Limit to first 1000 addresses

        # Find addresses that changed incrementally
        changing_addresses = []
        for addr, values in monitoring_data.items():
            if len(values) > 1:
                # Check if values increment by 1
                for i in range(1, len(values)):
                    if values[i][1] == values[i-1][1] + 1:
                        changing_addresses.append(addr)
                        break

        print(f"Found {len(changing_addresses)} incrementing addresses")

        # Return the most likely counter address
        if changing_addresses:
            return changing_addresses[0]

        return None

    def scan_for_small_integers(self) -> List[int]:
        """
        Scan for small integer values (0-100) that could be counters
        """
        addresses = []

        try:
            # Use a simpler approach - scan specific memory ranges
            # Get the main module base address
            modules = self.process.list_modules()
            if not modules:
                return addresses

            main_module = modules[0]  # Usually the main executable
            base_address = main_module.lpBaseOfDll

            # Scan in chunks around the main module
            scan_ranges = [
                (base_address, 1024 * 1024),  # 1MB from base
                (base_address + 1024 * 1024, 1024 * 1024),  # Next 1MB
                (base_address + 2 * 1024 * 1024, 1024 * 1024),  # Next 1MB
            ]

            for start_addr, size in scan_ranges:
                try:
                    # Try to read memory in smaller chunks
                    chunk_size = 64 * 1024  # 64KB chunks

                    for offset in range(0, size, chunk_size):
                        try:
                            current_addr = start_addr + offset
                            memory_data = self.process.read_bytes(current_addr, chunk_size)

                            # Look for small integers (0-100) stored as 32-bit values
                            for i in range(0, len(memory_data) - 4, 4):
                                try:
                                    value = struct.unpack('<I', memory_data[i:i+4])[0]
                                    if 0 <= value <= 100:  # Reasonable counter range
                                        addresses.append(current_addr + i)
                                except:
                                    continue

                        except (pymem.exception.MemoryReadError,
                               pymem.exception.WinAPIError):
                            continue

                except Exception:
                    continue

        except Exception as e:
            print(f"Error scanning for integers: {e}")

        return addresses
