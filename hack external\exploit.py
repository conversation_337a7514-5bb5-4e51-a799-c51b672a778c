#!/usr/bin/env python3
"""
External Counter Exploit - Main exploit implementation
Hooks into target applications and modifies increment behavior
"""

import time
import threading
import keyboard
from colorama import init, Fore, Style
from typing import Optional

from process_hook import ProcessHook
from memory_scanner import MemoryScanner
from config import HOTKEYS, EXPLOIT_SETTINGS, MEMORY_PATTERNS

# Initialize colorama for colored output
init()


class CounterExploit:
    def __init__(self):
        self.process_hook = ProcessHook()
        self.memory_scanner = None
        self.counter_address = None
        self.exploit_active = False
        self.running = True
        self.original_increment = 1
        self.target_increment = 10
        
    def print_status(self, message: str, status_type: str = "info"):
        """Print colored status message"""
        colors = {
            "info": Fore.CYAN,
            "success": Fore.GREEN,
            "warning": Fore.YELLOW,
            "error": Fore.RED,
            "exploit": Fore.MAGENTA
        }
        color = colors.get(status_type, Fore.WHITE)
        print(f"{color}[{status_type.upper()}]{Style.RESET_ALL} {message}")
    
    def setup_hotkeys(self):
        """Setup keyboard hotkeys for exploit control"""
        keyboard.add_hotkey(HOTKEYS["toggle_exploit"], self.toggle_exploit)
        keyboard.add_hotkey(HOTKEYS["manual_increment"], self.manual_increment)
        keyboard.add_hotkey(HOTKEYS["reset_counter"], self.reset_counter)
        keyboard.add_hotkey(HOTKEYS["exit_exploit"], self.exit_exploit)
        
        self.print_status("Hotkeys registered:", "info")
        self.print_status(f"  {HOTKEYS['toggle_exploit']} - Toggle exploit on/off", "info")
        self.print_status(f"  {HOTKEYS['manual_increment']} - Manual increment by {self.target_increment}", "info")
        self.print_status(f"  {HOTKEYS['reset_counter']} - Reset counter to 0", "info")
        self.print_status(f"  {HOTKEYS['exit_exploit']} - Exit exploit", "info")
    
    def find_counter_address(self, security_level: str) -> bool:
        """Find the counter address in target process memory"""
        if not self.memory_scanner:
            return False
            
        self.print_status(f"Searching for counter address ({security_level} level)...", "info")
        
        # Use dynamic scanning to find the counter
        self.counter_address = self.memory_scanner.find_dynamic_counter(security_level)
        
        if self.counter_address:
            self.print_status(f"Counter found at address: 0x{self.counter_address:08X}", "success")
            return True
        else:
            self.print_status("Counter address not found", "error")
            return False
    
    def hook_target_process(self) -> bool:
        """Hook into the target process"""
        self.print_status("Searching for target applications...", "info")
        
        success, security_level = self.process_hook.find_and_attach_target()
        
        if not success:
            self.print_status("No target applications found", "warning")
            return False
            
        self.print_status(f"Attached to target ({security_level} security level)", "success")
        
        # Initialize memory scanner
        self.memory_scanner = MemoryScanner(self.process_hook.target_process)
        
        # Find counter address
        if not self.find_counter_address(security_level):
            return False
            
        return True
    
    def monitor_and_modify(self):
        """Monitor target process and modify counter increments"""
        last_counter_value = None
        
        while self.running and self.process_hook.is_process_alive():
            if not self.exploit_active or not self.counter_address:
                time.sleep(0.1)
                continue
                
            try:
                # Read current counter value
                current_value = self.memory_scanner.read_int32(self.counter_address)
                
                if current_value is not None and last_counter_value is not None:
                    # Check if counter was incremented by 1 (normal behavior)
                    if current_value == last_counter_value + 1:
                        # Modify to increment by target amount instead
                        new_value = last_counter_value + self.target_increment
                        if self.memory_scanner.write_int32(self.counter_address, new_value):
                            self.print_status(
                                f"Modified increment: {last_counter_value} -> {new_value} "
                                f"(+{self.target_increment} instead of +1)", 
                                "exploit"
                            )
                
                last_counter_value = current_value
                
            except Exception as e:
                self.print_status(f"Error in monitor loop: {e}", "error")
                
            time.sleep(0.05)  # 20 FPS monitoring
    
    def toggle_exploit(self):
        """Toggle exploit on/off"""
        self.exploit_active = not self.exploit_active
        status = "ENABLED" if self.exploit_active else "DISABLED"
        self.print_status(f"Exploit {status}", "exploit")
    
    def manual_increment(self):
        """Manually increment counter by target amount"""
        if not self.counter_address:
            self.print_status("No counter address found", "error")
            return
            
        try:
            current_value = self.memory_scanner.read_int32(self.counter_address)
            if current_value is not None:
                new_value = current_value + self.target_increment
                if self.memory_scanner.write_int32(self.counter_address, new_value):
                    self.print_status(
                        f"Manual increment: {current_value} -> {new_value}", 
                        "exploit"
                    )
        except Exception as e:
            self.print_status(f"Manual increment failed: {e}", "error")
    
    def reset_counter(self):
        """Reset counter to 0"""
        if not self.counter_address:
            self.print_status("No counter address found", "error")
            return
            
        try:
            if self.memory_scanner.write_int32(self.counter_address, 0):
                self.print_status("Counter reset to 0", "exploit")
        except Exception as e:
            self.print_status(f"Counter reset failed: {e}", "error")
    
    def exit_exploit(self):
        """Exit the exploit"""
        self.print_status("Exiting exploit...", "info")
        self.running = False
    
    def run(self):
        """Main exploit loop"""
        self.print_status("=== External Counter Exploit ===", "info")
        self.print_status("Starting exploit system...", "info")
        
        # Setup hotkeys
        self.setup_hotkeys()
        
        # Hook target process
        if not self.hook_target_process():
            self.print_status("Failed to hook target process", "error")
            return
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_and_modify, daemon=True)
        monitor_thread.start()
        
        self.print_status("Exploit ready! Use hotkeys to control.", "success")
        self.print_status("Waiting for target application interaction...", "info")
        
        # Main loop
        try:
            while self.running:
                if not self.process_hook.is_process_alive():
                    self.print_status("Target process terminated", "warning")
                    break
                time.sleep(1.0)
        except KeyboardInterrupt:
            self.print_status("Interrupted by user", "info")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Cleanup resources"""
        self.running = False
        self.process_hook.detach()
        self.print_status("Cleanup completed", "info")


if __name__ == "__main__":
    exploit = CounterExploit()
    exploit.run()
