#!/usr/bin/env python3
"""
Working External Counter Exploit
Successfully identifies and modifies counter variables in Python applications
"""

import time
import threading
import keyboard
import psutil
import pymem
import struct
from colorama import init, Fore, Style
from typing import Optional, List, Dict, Tuple

# Initialize colorama for colored output
init()


class WorkingCounterExploit:
    def __init__(self):
        self.target_process = None
        self.target_pid = None
        self.counter_address = None
        self.exploit_active = False
        self.running = True
        self.last_known_value = 0
        self.candidate_addresses = {}
        self.monitoring_active = False
        
    def print_status(self, message: str, status_type: str = "info"):
        """Print colored status message"""
        colors = {
            "info": Fore.CYAN,
            "success": Fore.GREEN,
            "warning": Fore.YELLOW,
            "error": Fore.RED,
            "exploit": Fore.MAGENTA,
            "debug": Fore.BLUE
        }
        color = colors.get(status_type, Fore.WHITE)
        print(f"{color}[{status_type.upper()}]{Style.RESET_ALL} {message}")
    
    def find_target_process(self) -> bool:
        """Find and attach to target process"""
        self.print_status("Searching for target applications...", "info")
        
        # Look for Python processes running main.py
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] in ['python.exe', 'pythonw.exe']:
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and any('main.py' in arg for arg in cmdline):
                        # Check if it's one of our target applications
                        if any(target in ' '.join(cmdline) for target in ['simple', 'medium', 'hack']):
                            try:
                                self.target_process = pymem.Pymem()
                                self.target_process.open_process_from_id(proc.info['pid'])
                                self.target_pid = proc.info['pid']
                                
                                # Determine security level
                                security_level = "unknown"
                                if 'simple' in ' '.join(cmdline):
                                    security_level = "simple"
                                elif 'medium' in ' '.join(cmdline):
                                    security_level = "medium"
                                elif 'hack' in ' '.join(cmdline):
                                    security_level = "hack"
                                
                                self.print_status(f"Attached to {security_level} target (PID: {proc.info['pid']})", "success")
                                return True
                            except Exception as e:
                                self.print_status(f"Failed to attach to PID {proc.info['pid']}: {e}", "error")
                                continue
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return False
    
    def scan_memory_regions(self) -> List[Tuple[int, int]]:
        """Get readable/writable memory regions"""
        regions = []
        try:
            # Use pymem's built-in memory region enumeration
            current_address = 0
            while current_address < 0x7FFFFFFF:  # 32-bit address space limit
                try:
                    mbi = self.target_process.virtual_query(current_address)
                    
                    # Check if region is readable and writable
                    if (mbi.Protect & 0x04) or (mbi.Protect & 0x02):  # PAGE_READWRITE or PAGE_READONLY
                        if mbi.RegionSize > 0 and mbi.RegionSize < 50 * 1024 * 1024:  # Skip huge regions
                            regions.append((mbi.BaseAddress, mbi.RegionSize))
                    
                    current_address = mbi.BaseAddress + mbi.RegionSize
                except:
                    current_address += 0x1000  # Skip 4KB
                    
        except Exception as e:
            self.print_status(f"Error scanning memory regions: {e}", "debug")
            
        return regions
    
    def find_counter_candidates(self) -> Dict[int, int]:
        """Find potential counter addresses by scanning for small integers"""
        self.print_status("Scanning memory for potential counter values...", "info")
        candidates = {}
        
        regions = self.scan_memory_regions()
        self.print_status(f"Scanning {len(regions)} memory regions", "debug")
        
        for base_addr, size in regions[:20]:  # Limit to first 20 regions
            try:
                # Read memory in chunks
                chunk_size = min(size, 64 * 1024)  # 64KB chunks
                
                for offset in range(0, size, chunk_size):
                    try:
                        addr = base_addr + offset
                        read_size = min(chunk_size, size - offset)
                        data = self.target_process.read_bytes(addr, read_size)
                        
                        # Look for 32-bit integers with small values (0-100)
                        for i in range(0, len(data) - 4, 4):
                            try:
                                # Try both little and big endian
                                value_le = struct.unpack('<I', data[i:i+4])[0]
                                if 0 <= value_le <= 100:
                                    candidates[addr + i] = value_le
                                    
                            except struct.error:
                                continue
                                
                    except (pymem.exception.MemoryReadError, pymem.exception.WinAPIError):
                        continue
                        
            except Exception:
                continue
                
        self.print_status(f"Found {len(candidates)} potential counter addresses", "info")
        return candidates
    
    def monitor_for_changes(self, candidates: Dict[int, int]) -> Optional[int]:
        """Monitor candidate addresses for counter-like behavior"""
        self.print_status("Monitoring addresses for changes...", "info")
        self.print_status("Click the target application button NOW!", "warning")
        
        # Store initial values
        initial_values = {}
        for addr, value in candidates.items():
            initial_values[addr] = value
            
        # Monitor for changes over 15 seconds
        start_time = time.time()
        detected_changes = {}
        
        while time.time() - start_time < 15.0:
            for addr in list(candidates.keys())[:500]:  # Limit to 500 addresses for performance
                try:
                    current_value = struct.unpack('<I', self.target_process.read_bytes(addr, 4))[0]
                    initial_value = initial_values.get(addr, 0)
                    
                    # Look for incremental changes (counter behavior)
                    if current_value > initial_value and current_value <= initial_value + 10:
                        change = current_value - initial_value
                        if addr not in detected_changes:
                            detected_changes[addr] = []
                        detected_changes[addr].append((time.time(), initial_value, current_value, change))
                        
                        self.print_status(f"Change detected at 0x{addr:08X}: {initial_value} -> {current_value} (+{change})", "debug")
                        
                        # Update initial value for next comparison
                        initial_values[addr] = current_value
                        
                except (pymem.exception.MemoryReadError, pymem.exception.WinAPIError, struct.error):
                    continue
                    
            time.sleep(0.1)  # 10 FPS monitoring
            
        # Analyze detected changes to find the most likely counter
        best_candidate = None
        best_score = 0
        
        for addr, changes in detected_changes.items():
            # Score based on number of +1 increments
            score = sum(1 for _, _, _, change in changes if change == 1)
            if score > best_score:
                best_score = score
                best_candidate = addr
                
        if best_candidate:
            self.print_status(f"Counter identified at address: 0x{best_candidate:08X}", "success")
            self.print_status(f"Detected {best_score} increment operations", "success")
            return best_candidate
        else:
            self.print_status("No counter address identified", "error")
            return None
    
    def setup_hotkeys(self):
        """Setup keyboard hotkeys"""
        keyboard.add_hotkey('F1', self.toggle_exploit)
        keyboard.add_hotkey('F2', self.manual_increment)
        keyboard.add_hotkey('F3', self.reset_counter)
        keyboard.add_hotkey('F4', self.exit_exploit)
        
        self.print_status("Hotkeys registered:", "info")
        self.print_status("  F1 - Toggle exploit on/off", "info")
        self.print_status("  F2 - Manual increment by 10", "info")
        self.print_status("  F3 - Reset counter to 0", "info")
        self.print_status("  F4 - Exit exploit", "info")
    
    def toggle_exploit(self):
        """Toggle exploit on/off"""
        self.exploit_active = not self.exploit_active
        status = "ENABLED" if self.exploit_active else "DISABLED"
        self.print_status(f"Exploit {status}", "exploit")
        
        if self.exploit_active:
            self.print_status("Now intercepting counter increments!", "exploit")
            self.print_status("Click the target app button to see +10 instead of +1!", "warning")
    
    def manual_increment(self):
        """Manually add 10 to counter"""
        if self.counter_address:
            try:
                current = struct.unpack('<I', self.target_process.read_bytes(self.counter_address, 4))[0]
                new_value = current + 10
                self.target_process.write_bytes(self.counter_address, struct.pack('<I', new_value), 4)
                self.print_status(f"Manual increment: {current} -> {new_value}", "exploit")
                self.last_known_value = new_value
            except Exception as e:
                self.print_status(f"Manual increment failed: {e}", "error")
    
    def reset_counter(self):
        """Reset counter to 0"""
        if self.counter_address:
            try:
                self.target_process.write_bytes(self.counter_address, struct.pack('<I', 0), 4)
                self.print_status("Counter reset to 0", "exploit")
                self.last_known_value = 0
            except Exception as e:
                self.print_status(f"Reset failed: {e}", "error")
    
    def exit_exploit(self):
        """Exit exploit"""
        self.running = False
        self.monitoring_active = False
        self.print_status("Exiting exploit...", "info")
    
    def monitor_and_intercept(self):
        """Main monitoring and interception loop"""
        self.monitoring_active = True
        
        while self.running and self.monitoring_active and self.counter_address:
            try:
                current_value = struct.unpack('<I', self.target_process.read_bytes(self.counter_address, 4))[0]
                
                if self.exploit_active:
                    # Check if counter increased by 1 (normal increment)
                    if current_value == self.last_known_value + 1:
                        # Intercept and change to +10
                        new_value = self.last_known_value + 10
                        self.target_process.write_bytes(self.counter_address, struct.pack('<I', new_value), 4)
                        
                        self.print_status(f"INTERCEPTED! {self.last_known_value} -> {new_value} (+10 instead of +1)", "exploit")
                        self.last_known_value = new_value
                    else:
                        self.last_known_value = current_value
                else:
                    self.last_known_value = current_value
                    
            except Exception as e:
                self.print_status(f"Monitor error: {e}", "debug")
                
            time.sleep(0.02)  # 50 FPS monitoring for responsiveness
    
    def run(self):
        """Main exploit function"""
        self.print_status("=== Working Counter Exploit ===", "info")
        self.print_status("Advanced memory manipulation for Python applications", "info")
        print()
        
        # Find and attach to target
        if not self.find_target_process():
            self.print_status("No target process found", "error")
            self.print_status("Please start a target application first:", "info")
            self.print_status("cd '../simple test external hack' && python main.py", "info")
            return
        
        # Find counter candidates
        candidates = self.find_counter_candidates()
        if not candidates:
            self.print_status("No potential counter addresses found", "error")
            return
        
        # Monitor for counter changes
        self.counter_address = self.monitor_for_changes(candidates)
        if not self.counter_address:
            self.print_status("Could not identify counter address", "error")
            self.print_status("Make sure to click the target app button during monitoring!", "warning")
            return
        
        # Initialize last known value
        try:
            self.last_known_value = struct.unpack('<I', self.target_process.read_bytes(self.counter_address, 4))[0]
        except:
            self.last_known_value = 0
        
        # Setup hotkeys
        self.setup_hotkeys()
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_and_intercept, daemon=True)
        monitor_thread.start()
        
        self.print_status("Exploit ready! Press F1 to enable interception.", "success")
        self.print_status("Then click the target app button to see the exploit in action!", "warning")
        print()
        
        # Main loop
        try:
            while self.running:
                time.sleep(1.0)
        except KeyboardInterrupt:
            self.print_status("Interrupted", "info")
        finally:
            self.monitoring_active = False
            if self.target_process:
                try:
                    self.target_process.close_handle()
                except:
                    pass
            self.print_status("Exploit terminated", "info")


if __name__ == "__main__":
    print(f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                  WORKING COUNTER EXPLOIT                    ║
║              Advanced Memory Manipulation                   ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}

{Fore.YELLOW}This exploit successfully identifies and modifies counter variables{Style.RESET_ALL}
{Fore.YELLOW}in real-time by intercepting memory writes.{Style.RESET_ALL}

""")
    
    exploit = WorkingCounterExploit()
    exploit.run()
