#!/usr/bin/env python3
"""
Test script for the external counter exploit
"""

import subprocess
import time
import sys
import os
from colorama import init, Fore, Style

init()

def print_status(message, status_type="info"):
    """Print colored status message"""
    colors = {
        "info": Fore.CYAN,
        "success": Fore.GREEN,
        "warning": Fore.YELLOW,
        "error": Fore.RED
    }
    color = colors.get(status_type, Fore.WHITE)
    print(f"{color}[{status_type.upper()}]{Style.RESET_ALL} {message}")

def test_dependencies():
    """Test if all dependencies are available"""
    print_status("Testing dependencies...")
    
    required_modules = ['pymem', 'psutil', 'keyboard', 'colorama']
    
    for module in required_modules:
        try:
            __import__(module)
            print_status(f"✓ {module} available", "success")
        except ImportError:
            print_status(f"✗ {module} missing", "error")
            return False
    
    return True

def test_imports():
    """Test if our modules can be imported"""
    print_status("Testing module imports...")
    
    try:
        from config import TARGET_PROCESSES, TARGET_APPS, MEMORY_PATTERNS
        print_status("✓ config.py imported", "success")
    except Exception as e:
        print_status(f"✗ config.py import failed: {e}", "error")
        return False
    
    try:
        from process_hook import ProcessHook
        print_status("✓ process_hook.py imported", "success")
    except Exception as e:
        print_status(f"✗ process_hook.py import failed: {e}", "error")
        return False
    
    try:
        from memory_scanner import MemoryScanner
        print_status("✓ memory_scanner.py imported", "success")
    except Exception as e:
        print_status(f"✗ memory_scanner.py import failed: {e}", "error")
        return False
    
    return True

def test_process_detection():
    """Test process detection functionality"""
    print_status("Testing process detection...")
    
    try:
        from process_hook import ProcessHook
        hook = ProcessHook()
        
        # Test finding processes
        targets = hook.find_target_processes()
        print_status(f"Found {len(targets)} potential target processes", "info")
        
        for pid, name, title in targets:
            print_status(f"  PID: {pid}, Name: {name}, Title: {title}", "info")
        
        return True
    except Exception as e:
        print_status(f"Process detection test failed: {e}", "error")
        return False

def main():
    """Main test function"""
    print_status("=== External Counter Exploit Test ===", "info")
    
    # Test dependencies
    if not test_dependencies():
        print_status("Dependency test failed", "error")
        return False
    
    # Test imports
    if not test_imports():
        print_status("Import test failed", "error")
        return False
    
    # Test process detection
    if not test_process_detection():
        print_status("Process detection test failed", "error")
        return False
    
    print_status("All tests passed! Exploit should work correctly.", "success")
    print_status("To test with a target application:", "info")
    print_status("1. Start a target app: cd '../simple test external hack' && python main.py", "info")
    print_status("2. Run the exploit: python run_exploit.py", "info")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
