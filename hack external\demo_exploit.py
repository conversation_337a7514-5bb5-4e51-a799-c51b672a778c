#!/usr/bin/env python3
"""
Demo External Counter Exploit
Demonstrates the concept with a working proof-of-concept
"""

import time
import threading
import keyboard
import psutil
import pymem
import random
from colorama import init, Fore, Style

# Initialize colorama for colored output
init()


class DemoCounterExploit:
    def __init__(self):
        self.target_process = None
        self.target_pid = None
        self.exploit_active = False
        self.running = True
        self.demo_counter = 0
        self.simulated_addresses = []
        
    def print_status(self, message: str, status_type: str = "info"):
        """Print colored status message"""
        colors = {
            "info": Fore.CYAN,
            "success": Fore.GREEN,
            "warning": Fore.YELLOW,
            "error": Fore.RED,
            "exploit": Fore.MAGENTA
        }
        color = colors.get(status_type, Fore.WHITE)
        print(f"{color}[{status_type.upper()}]{Style.RESET_ALL} {message}")
    
    def find_target_process(self) -> bool:
        """Find and attach to target process"""
        self.print_status("Searching for target applications...", "info")
        
        # Look for Python processes
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] in ['python.exe', 'pythonw.exe']:
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and 'main.py' in ' '.join(cmdline):
                        # Found a potential target
                        try:
                            self.target_process = pymem.Pymem()
                            self.target_process.open_process_from_id(proc.info['pid'])
                            self.target_pid = proc.info['pid']
                            self.print_status(f"Attached to process PID: {proc.info['pid']}", "success")
                            return True
                        except Exception as e:
                            self.print_status(f"Failed to attach to PID {proc.info['pid']}: {e}", "error")
                            continue
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return False
    
    def simulate_memory_scan(self):
        """Simulate finding counter addresses"""
        self.print_status("Scanning for counter addresses...", "info")
        time.sleep(2)  # Simulate scanning time
        
        # Generate some fake addresses for demonstration
        base_addr = 0x10000000
        self.simulated_addresses = [base_addr + i * 4 for i in range(1000)]
        
        self.print_status(f"Found {len(self.simulated_addresses)} potential addresses", "info")
        return True
    
    def simulate_counter_detection(self):
        """Simulate detecting the counter address"""
        self.print_status("Monitoring for counter changes...", "info")
        self.print_status("Click the target application button to help identify the counter!", "warning")
        
        # Simulate monitoring time
        for i in range(10):
            time.sleep(1)
            print(".", end="", flush=True)
        print()
        
        # Simulate finding the counter
        counter_addr = self.simulated_addresses[42]  # Pick a "random" address
        self.print_status(f"Counter found at address: 0x{counter_addr:08X}", "success")
        return counter_addr
    
    def setup_hotkeys(self):
        """Setup keyboard hotkeys"""
        keyboard.add_hotkey('F1', self.toggle_exploit)
        keyboard.add_hotkey('F2', self.manual_increment)
        keyboard.add_hotkey('F3', self.reset_counter)
        keyboard.add_hotkey('F4', self.exit_exploit)
        
        self.print_status("Hotkeys registered:", "info")
        self.print_status("  F1 - Toggle exploit on/off", "info")
        self.print_status("  F2 - Manual increment by 10", "info")
        self.print_status("  F3 - Reset counter to 0", "info")
        self.print_status("  F4 - Exit exploit", "info")
    
    def toggle_exploit(self):
        """Toggle exploit on/off"""
        self.exploit_active = not self.exploit_active
        status = "ENABLED" if self.exploit_active else "DISABLED"
        self.print_status(f"Exploit {status}", "exploit")
        
        if self.exploit_active:
            self.print_status("Now intercepting counter increments!", "exploit")
            self.print_status("Click the target app button to see the exploit in action!", "warning")
    
    def manual_increment(self):
        """Manually add 10 to counter"""
        self.demo_counter += 10
        self.print_status(f"Manual increment: Counter is now {self.demo_counter}", "exploit")
        
        if self.target_process:
            self.print_status("(In real exploit: writing +10 to memory address)", "info")
    
    def reset_counter(self):
        """Reset counter to 0"""
        self.demo_counter = 0
        self.print_status("Counter reset to 0", "exploit")
        
        if self.target_process:
            self.print_status("(In real exploit: writing 0 to memory address)", "info")
    
    def exit_exploit(self):
        """Exit exploit"""
        self.running = False
        self.print_status("Exiting exploit...", "info")
    
    def simulate_interception(self):
        """Simulate intercepting counter increments"""
        while self.running:
            if self.exploit_active and self.target_process:
                # Simulate detecting a +1 increment and changing it to +10
                if random.random() < 0.1:  # 10% chance per second
                    old_value = self.demo_counter
                    self.demo_counter += 10  # Simulate changing +1 to +10
                    self.print_status(
                        f"INTERCEPTED! Changed increment from +1 to +10: {old_value} -> {self.demo_counter}", 
                        "exploit"
                    )
                    self.print_status("(In real exploit: detected +1 in memory, overwrote with +10)", "info")
                    
            time.sleep(1.0)
    
    def run(self):
        """Main exploit function"""
        self.print_status("=== Demo Counter Exploit ===", "info")
        self.print_status("This demonstrates the external memory manipulation concept", "warning")
        print()
        
        # Find target
        if not self.find_target_process():
            self.print_status("No target process found - running in demo mode", "warning")
            self.print_status("(In real scenario: would attach to target application)", "info")
        
        # Simulate memory scanning
        if not self.simulate_memory_scan():
            self.print_status("Memory scan failed", "error")
            return
        
        # Simulate counter detection
        counter_address = self.simulate_counter_detection()
        if not counter_address:
            self.print_status("Could not identify counter", "error")
            return
        
        # Setup hotkeys
        self.setup_hotkeys()
        
        # Start simulation thread
        sim_thread = threading.Thread(target=self.simulate_interception, daemon=True)
        sim_thread.start()
        
        self.print_status("Demo exploit ready!", "success")
        self.print_status("Press F1 to enable the exploit simulation", "info")
        print()
        
        # Show how it works
        self.print_status("HOW THE REAL EXPLOIT WORKS:", "info")
        self.print_status("1. Scans target process memory for counter variable", "info")
        self.print_status("2. Monitors memory location for changes", "info")
        self.print_status("3. When counter increments by +1, immediately overwrites with +10", "info")
        self.print_status("4. Target application shows +10 increment instead of +1", "info")
        print()
        
        # Main loop
        try:
            while self.running:
                time.sleep(1.0)
        except KeyboardInterrupt:
            self.print_status("Interrupted", "info")
        finally:
            if self.target_process:
                self.target_process.close_handle()
            self.print_status("Demo completed", "info")


if __name__ == "__main__":
    print(f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    DEMO COUNTER EXPLOIT                     ║
║                  Proof of Concept Demo                      ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}

{Fore.YELLOW}This demo shows how the external memory manipulation exploit works.{Style.RESET_ALL}
{Fore.YELLOW}The real exploit would modify actual memory addresses in the target.{Style.RESET_ALL}

""")
    
    exploit = DemoCounterExploit()
    exploit.run()
