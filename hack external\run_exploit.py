#!/usr/bin/env python3
"""
Easy-to-use launcher for the external counter exploit
"""

import sys
import os
import subprocess
import time
from colorama import init, Fore, Style

init()

def print_banner():
    """Print exploit banner"""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    EXTERNAL COUNTER EXPLOIT                 ║
║                         Version 1.0                         ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}

{Fore.YELLOW}WARNING: This tool is for educational purposes only!{Style.RESET_ALL}
{Fore.YELLOW}Only use on applications you own or have permission to test.{Style.RESET_ALL}

"""
    print(banner)

def check_dependencies():
    """Check if required dependencies are installed"""
    print(f"{Fore.CYAN}[INFO]{Style.RESET_ALL} Checking dependencies...")
    
    required_modules = ['pymem', 'psutil', 'keyboard', 'colorama']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"{Fore.GREEN}✓{Style.RESET_ALL} {module}")
        except ImportError:
            print(f"{Fore.RED}✗{Style.RESET_ALL} {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n{Fore.RED}[ERROR]{Style.RESET_ALL} Missing dependencies: {', '.join(missing_modules)}")
        print(f"{Fore.YELLOW}[INFO]{Style.RESET_ALL} Install with: pip install -r requirements.txt")
        return False
    
    print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} All dependencies satisfied!")
    return True

def show_instructions():
    """Show usage instructions"""
    instructions = f"""
{Fore.CYAN}INSTRUCTIONS:{Style.RESET_ALL}

1. {Fore.YELLOW}Start a target application:{Style.RESET_ALL}
   - Navigate to one of the test directories (simple/medium/hack)
   - Run: python main.py

2. {Fore.YELLOW}Run this exploit:{Style.RESET_ALL}
   - The exploit will automatically find and attach to the target
   - Use the hotkeys to control the exploit

3. {Fore.YELLOW}Hotkeys:{Style.RESET_ALL}
   - F1: Toggle exploit on/off
   - F2: Manual increment by 10
   - F3: Reset counter to 0
   - F4: Exit exploit

4. {Fore.YELLOW}How it works:{Style.RESET_ALL}
   - The exploit scans the target process memory
   - Finds the counter variable location
   - Intercepts increment operations and changes them from +1 to +10

{Fore.GREEN}Ready to start? Press Enter to continue or Ctrl+C to exit...{Style.RESET_ALL}
"""
    print(instructions)
    
    try:
        input()
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}[INFO]{Style.RESET_ALL} Exiting...")
        sys.exit(0)

def main():
    """Main launcher function"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Show instructions
    show_instructions()
    
    # Launch exploit
    print(f"{Fore.CYAN}[INFO]{Style.RESET_ALL} Starting exploit...")
    
    try:
        # Import and run exploit
        from exploit import CounterExploit
        exploit = CounterExploit()
        exploit.run()
    except ImportError as e:
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} Failed to import exploit: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} Exploit error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
