#!/usr/bin/env python3
"""
Setup script for the external counter exploit
"""

import subprocess
import sys
import os
from colorama import init, Fore, Style

init()

def print_status(message, status_type="info"):
    """Print colored status message"""
    colors = {
        "info": Fore.CYAN,
        "success": Fore.GREEN,
        "warning": Fore.YELLOW,
        "error": Fore.RED
    }
    color = colors.get(status_type, Fore.WHITE)
    print(f"{color}[{status_type.upper()}]{Style.RESET_ALL} {message}")

def install_dependencies():
    """Install required dependencies"""
    print_status("Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print_status("Dependencies installed successfully", "success")
        return True
    except subprocess.CalledProcessError as e:
        print_status(f"Failed to install dependencies: {e}", "error")
        return False

def test_installation():
    """Test if installation was successful"""
    print_status("Testing installation...")
    
    try:
        subprocess.check_call([sys.executable, "test_exploit.py"])
        print_status("Installation test passed", "success")
        return True
    except subprocess.CalledProcessError:
        print_status("Installation test failed", "error")
        return False

def main():
    """Main setup function"""
    print_status("=== External Counter Exploit Setup ===", "info")
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Test installation
    if not test_installation():
        return False
    
    print_status("Setup completed successfully!", "success")
    print_status("You can now run the exploit with: python run_exploit.py", "info")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
