#!/usr/bin/env python3
"""
Configuration settings for the external exploit
"""

# Target process names to look for
TARGET_PROCESSES = [
    "python.exe",
    "python3.exe",
    "pythonw.exe"
]

# Target application identifiers (window titles)
TARGET_APPS = {
    "simple": "Simple Counter - Easy to Hack",
    "medium": "Medium Counter - Moderate Security", 
    "hack": "Hack Counter - High Security"
}

# Memory scan patterns for different security levels
MEMORY_PATTERNS = {
    "simple": {
        "counter_patterns": [
            b"\x00\x00\x00\x00",  # 32-bit integer 0
            b"\x01\x00\x00\x00",  # 32-bit integer 1
            b"\x02\x00\x00\x00",  # 32-bit integer 2
        ],
        "increment_value": 10,  # Change increment from 1 to 10
        "max_scan_size": 50 * 1024 * 1024  # 50MB
    },
    "medium": {
        "counter_patterns": [
            b"\x00\x00\x00\x00",  # Look for the real counter (_x7f2a)
        ],
        "increment_value": 10,
        "max_scan_size": 100 * 1024 * 1024  # 100MB
    },
    "hack": {
        "counter_patterns": [
            b"\x00\x00\x00\x00",
        ],
        "increment_value": 10,
        "max_scan_size": 150 * 1024 * 1024  # 150MB
    }
}

# Hotkeys for the exploit
HOTKEYS = {
    "toggle_exploit": "F1",
    "manual_increment": "F2", 
    "reset_counter": "F3",
    "exit_exploit": "F4"
}

# Exploit settings
EXPLOIT_SETTINGS = {
    "auto_hook": True,
    "scan_interval": 1.0,  # seconds
    "debug_mode": True,
    "log_file": "exploit.log"
}
