#!/usr/bin/env python3
"""
Simplified External Counter Exploit
Uses a more direct approach for Python applications
"""

import time
import threading
import keyboard
import psutil
import pymem
from colorama import init, Fore, Style
from typing import Optional, List

# Initialize colorama for colored output
init()


class SimpleCounterExploit:
    def __init__(self):
        self.target_process = None
        self.target_pid = None
        self.counter_address = None
        self.exploit_active = False
        self.running = True
        self.last_counter_value = 0

    def print_status(self, message: str, status_type: str = "info"):
        """Print colored status message"""
        colors = {
            "info": Fore.CYAN,
            "success": Fore.GREEN,
            "warning": Fore.YELLOW,
            "error": Fore.RED,
            "exploit": Fore.MAGENTA
        }
        color = colors.get(status_type, Fore.WHITE)
        print(f"{color}[{status_type.upper()}]{Style.RESET_ALL} {message}")

    def find_target_process(self) -> bool:
        """Find and attach to target process"""
        self.print_status("Searching for target applications...", "info")

        # Look for Python processes
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] in ['python.exe', 'pythonw.exe']:
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and 'main.py' in ' '.join(cmdline):
                        # Found a potential target
                        try:
                            self.target_process = pymem.Pymem()
                            self.target_process.open_process_from_id(proc.info['pid'])
                            self.target_pid = proc.info['pid']
                            self.print_status(f"Attached to process PID: {proc.info['pid']}", "success")
                            return True
                        except Exception as e:
                            self.print_status(f"Failed to attach to PID {proc.info['pid']}: {e}", "error")
                            continue
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return False

    def scan_for_counter(self) -> Optional[int]:
        """Scan for counter value using a simple approach"""
        self.print_status("Scanning for counter addresses...", "info")

        if not self.target_process:
            return None

        # Try to find addresses containing small values (0-10)
        potential_addresses = []

        try:
            # Get modules and scan their memory
            modules = list(self.target_process.list_modules())

            for module in modules[:3]:  # Only scan first 3 modules
                try:
                    base_addr = module.lpBaseOfDll
                    size = min(module.SizeOfImage, 1024 * 1024)  # Limit to 1MB

                    # Read memory in chunks
                    chunk_size = 4096  # 4KB chunks
                    for offset in range(0, size, chunk_size):
                        try:
                            addr = base_addr + offset
                            data = self.target_process.read_bytes(addr, min(chunk_size, size - offset))

                            # Look for 32-bit integers with small values
                            for i in range(0, len(data) - 4, 4):
                                try:
                                    value = int.from_bytes(data[i:i+4], 'little', signed=False)
                                    if 0 <= value <= 50:  # Counter likely to be small
                                        potential_addresses.append(addr + i)
                                except:
                                    continue

                        except (pymem.exception.MemoryReadError, pymem.exception.WinAPIError):
                            continue

                except Exception:
                    continue

        except Exception as e:
            self.print_status(f"Error during memory scan: {e}", "error")

        self.print_status(f"Found {len(potential_addresses)} potential addresses", "info")

        # Return first few addresses to monitor
        return potential_addresses[:100] if potential_addresses else []

    def monitor_memory(self, addresses: List[int]) -> Optional[int]:
        """Monitor addresses for changes"""
        self.print_status("Monitoring for counter changes (click the target app button)...", "info")

        # Store initial values
        initial_values = {}
        for addr in addresses:
            try:
                value = self.target_process.read_int(addr)
                initial_values[addr] = value
            except:
                continue

        # Monitor for 10 seconds
        start_time = time.time()
        while time.time() - start_time < 10.0:
            for addr in addresses:
                try:
                    current_value = self.target_process.read_int(addr)
                    initial_value = initial_values.get(addr, 0)

                    # Check if value increased by 1 (typical counter behavior)
                    if current_value == initial_value + 1:
                        self.print_status(f"Found counter at address: 0x{addr:08X}", "success")
                        return addr

                except:
                    continue

            time.sleep(0.1)

        return None

    def setup_hotkeys(self):
        """Setup keyboard hotkeys"""
        keyboard.add_hotkey('F1', self.toggle_exploit)
        keyboard.add_hotkey('F2', self.manual_increment)
        keyboard.add_hotkey('F3', self.reset_counter)
        keyboard.add_hotkey('F4', self.exit_exploit)

        self.print_status("Hotkeys: F1=Toggle, F2=+10, F3=Reset, F4=Exit", "info")

    def toggle_exploit(self):
        """Toggle exploit on/off"""
        self.exploit_active = not self.exploit_active
        status = "ENABLED" if self.exploit_active else "DISABLED"
        self.print_status(f"Exploit {status}", "exploit")

    def manual_increment(self):
        """Manually add 10 to counter"""
        if self.counter_address:
            try:
                current = self.target_process.read_int(self.counter_address)
                self.target_process.write_int(self.counter_address, current + 10)
                self.print_status(f"Manual increment: {current} -> {current + 10}", "exploit")
            except Exception as e:
                self.print_status(f"Manual increment failed: {e}", "error")

    def reset_counter(self):
        """Reset counter to 0"""
        if self.counter_address:
            try:
                self.target_process.write_int(self.counter_address, 0)
                self.print_status("Counter reset to 0", "exploit")
            except Exception as e:
                self.print_status(f"Reset failed: {e}", "error")

    def exit_exploit(self):
        """Exit exploit"""
        self.running = False
        self.print_status("Exiting...", "info")

    def monitor_loop(self):
        """Main monitoring loop"""
        while self.running and self.counter_address:
            if self.exploit_active:
                try:
                    current_value = self.target_process.read_int(self.counter_address)

                    # Check if counter increased by 1
                    if current_value == self.last_counter_value + 1:
                        # Change it to +10 instead
                        new_value = self.last_counter_value + 10
                        self.target_process.write_int(self.counter_address, new_value)
                        self.print_status(f"Intercepted: {self.last_counter_value} -> {new_value}", "exploit")
                        self.last_counter_value = new_value
                    else:
                        self.last_counter_value = current_value

                except Exception as e:
                    self.print_status(f"Monitor error: {e}", "error")

            time.sleep(0.05)  # 20 FPS

    def run(self):
        """Main exploit function"""
        self.print_status("=== Simple Counter Exploit ===", "info")

        # Find target
        if not self.find_target_process():
            self.print_status("No target process found", "error")
            return

        # Scan for counter
        addresses = self.scan_for_counter()
        if not addresses:
            self.print_status("No potential addresses found", "error")
            return

        # Monitor for changes
        self.counter_address = self.monitor_memory(addresses)
        if not self.counter_address:
            self.print_status("Could not identify counter address", "error")
            return

        # Setup hotkeys and start monitoring
        self.setup_hotkeys()

        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        monitor_thread.start()

        self.print_status("Exploit ready! Press F1 to enable.", "success")

        # Main loop
        try:
            while self.running:
                time.sleep(1.0)
        except KeyboardInterrupt:
            self.print_status("Interrupted", "info")
        finally:
            if self.target_process:
                self.target_process.close_handle()


if __name__ == "__main__":
    exploit = SimpleCounterExploit()
    exploit.run()
