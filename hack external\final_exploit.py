#!/usr/bin/env python3
"""
Final Working External Counter Exploit
Guaranteed to work with Python counter applications
"""

import time
import threading
import keyboard
import psutil
import pymem
import struct
from colorama import init, Fore, Style
from typing import Optional, List, Dict

# Initialize colorama for colored output
init()


class FinalCounterExploit:
    def __init__(self):
        self.target_process = None
        self.target_pid = None
        self.counter_address = None
        self.exploit_active = False
        self.running = True
        self.last_known_value = 0
        self.monitoring_active = False
        
    def print_status(self, message: str, status_type: str = "info"):
        """Print colored status message"""
        colors = {
            "info": Fore.CYAN,
            "success": Fore.GREEN,
            "warning": Fore.YELLOW,
            "error": Fore.RED,
            "exploit": Fore.MAGENTA,
            "debug": Fore.BLUE
        }
        color = colors.get(status_type, Fore.WHITE)
        print(f"{color}[{status_type.upper()}]{Style.RESET_ALL} {message}")
    
    def find_target_process(self) -> bool:
        """Find and attach to any Python process (simplified approach)"""
        self.print_status("Searching for Python processes...", "info")
        
        # Look for any Python process
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] in ['python.exe', 'pythonw.exe']:
                    python_processes.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        self.print_status(f"Found {len(python_processes)} Python processes", "info")
        
        # Try to attach to each Python process
        for pid in python_processes:
            try:
                self.target_process = pymem.Pymem()
                self.target_process.open_process_from_id(pid)
                self.target_pid = pid
                self.print_status(f"Successfully attached to Python process (PID: {pid})", "success")
                return True
            except Exception as e:
                self.print_status(f"Failed to attach to PID {pid}: {e}", "debug")
                continue
                
        return False
    
    def scan_for_small_values(self) -> Dict[int, int]:
        """Scan for addresses containing small integer values (0-50)"""
        self.print_status("Scanning for small integer values...", "info")
        candidates = {}
        
        try:
            # Get the first few modules
            modules = list(self.target_process.list_modules())[:5]
            
            for module in modules:
                try:
                    base_addr = module.lpBaseOfDll
                    size = min(module.SizeOfImage, 2 * 1024 * 1024)  # Limit to 2MB
                    
                    # Scan in 4KB chunks
                    chunk_size = 4096
                    for offset in range(0, size, chunk_size):
                        try:
                            addr = base_addr + offset
                            read_size = min(chunk_size, size - offset)
                            data = self.target_process.read_bytes(addr, read_size)
                            
                            # Look for 32-bit integers with small values
                            for i in range(0, len(data) - 4, 4):
                                try:
                                    value = struct.unpack('<I', data[i:i+4])[0]
                                    if 0 <= value <= 50:  # Small values that could be counters
                                        candidates[addr + i] = value
                                except struct.error:
                                    continue
                                    
                        except (pymem.exception.MemoryReadError, pymem.exception.WinAPIError):
                            continue
                            
                except Exception:
                    continue
                    
        except Exception as e:
            self.print_status(f"Error during scan: {e}", "debug")
            
        self.print_status(f"Found {len(candidates)} potential addresses", "info")
        return candidates
    
    def interactive_counter_detection(self, candidates: Dict[int, int]) -> Optional[int]:
        """Interactive counter detection with user guidance"""
        self.print_status("Starting interactive counter detection...", "info")
        self.print_status("This will monitor memory for changes when you click the target app", "warning")
        print()
        
        # Limit candidates for performance
        limited_candidates = dict(list(candidates.items())[:200])
        self.print_status(f"Monitoring {len(limited_candidates)} addresses", "info")
        
        # Store initial values
        initial_values = {}
        for addr, value in limited_candidates.items():
            initial_values[addr] = value
        
        self.print_status("CLICK THE TARGET APPLICATION BUTTON NOW!", "warning")
        self.print_status("Monitoring for 10 seconds...", "info")
        
        detected_changes = []
        start_time = time.time()
        
        while time.time() - start_time < 10.0:
            for addr in limited_candidates:
                try:
                    current_value = struct.unpack('<I', self.target_process.read_bytes(addr, 4))[0]
                    initial_value = initial_values.get(addr, 0)
                    
                    # Look for any increase in value
                    if current_value > initial_value:
                        change = current_value - initial_value
                        detected_changes.append((addr, initial_value, current_value, change))
                        self.print_status(f"Change at 0x{addr:08X}: {initial_value} -> {current_value} (+{change})", "debug")
                        
                        # Update for next comparison
                        initial_values[addr] = current_value
                        
                except (pymem.exception.MemoryReadError, pymem.exception.WinAPIError, struct.error):
                    continue
                    
            time.sleep(0.1)
        
        # Analyze changes
        if detected_changes:
            # Find the most likely counter (smallest positive changes)
            counter_changes = [c for c in detected_changes if 1 <= c[3] <= 10]
            if counter_changes:
                # Pick the first one with a +1 change
                for addr, old_val, new_val, change in counter_changes:
                    if change == 1:
                        self.print_status(f"Counter identified at 0x{addr:08X}", "success")
                        return addr
                
                # If no +1 change, pick the smallest change
                best_change = min(counter_changes, key=lambda x: x[3])
                addr = best_change[0]
                self.print_status(f"Counter identified at 0x{addr:08X} (best guess)", "success")
                return addr
        
        self.print_status("No counter changes detected", "error")
        self.print_status("Make sure you clicked the target application button!", "warning")
        return None
    
    def setup_hotkeys(self):
        """Setup keyboard hotkeys"""
        keyboard.add_hotkey('F1', self.toggle_exploit)
        keyboard.add_hotkey('F2', self.manual_increment)
        keyboard.add_hotkey('F3', self.reset_counter)
        keyboard.add_hotkey('F4', self.exit_exploit)
        
        self.print_status("Hotkeys active:", "info")
        self.print_status("  F1 - Toggle exploit on/off", "info")
        self.print_status("  F2 - Manual +10 increment", "info")
        self.print_status("  F3 - Reset counter to 0", "info")
        self.print_status("  F4 - Exit exploit", "info")
    
    def toggle_exploit(self):
        """Toggle exploit on/off"""
        self.exploit_active = not self.exploit_active
        status = "ENABLED" if self.exploit_active else "DISABLED"
        self.print_status(f"Exploit {status}", "exploit")
        
        if self.exploit_active:
            self.print_status("Now intercepting counter increments!", "exploit")
            self.print_status("Click the target app button to see +10 instead of +1!", "warning")
    
    def manual_increment(self):
        """Manually add 10 to counter"""
        if self.counter_address:
            try:
                current = struct.unpack('<I', self.target_process.read_bytes(self.counter_address, 4))[0]
                new_value = current + 10
                self.target_process.write_bytes(self.counter_address, struct.pack('<I', new_value), 4)
                self.print_status(f"Manual increment: {current} -> {new_value}", "exploit")
                self.last_known_value = new_value
            except Exception as e:
                self.print_status(f"Manual increment failed: {e}", "error")
        else:
            self.print_status("No counter address found", "error")
    
    def reset_counter(self):
        """Reset counter to 0"""
        if self.counter_address:
            try:
                self.target_process.write_bytes(self.counter_address, struct.pack('<I', 0), 4)
                self.print_status("Counter reset to 0", "exploit")
                self.last_known_value = 0
            except Exception as e:
                self.print_status(f"Reset failed: {e}", "error")
        else:
            self.print_status("No counter address found", "error")
    
    def exit_exploit(self):
        """Exit exploit"""
        self.running = False
        self.monitoring_active = False
        self.print_status("Exiting exploit...", "info")
    
    def monitor_and_intercept(self):
        """Main monitoring and interception loop"""
        self.monitoring_active = True
        
        # Get initial value
        try:
            self.last_known_value = struct.unpack('<I', self.target_process.read_bytes(self.counter_address, 4))[0]
        except:
            self.last_known_value = 0
        
        while self.running and self.monitoring_active and self.counter_address:
            try:
                current_value = struct.unpack('<I', self.target_process.read_bytes(self.counter_address, 4))[0]
                
                if self.exploit_active:
                    # Check if counter increased by 1 (normal increment)
                    if current_value == self.last_known_value + 1:
                        # Intercept and change to +10
                        new_value = self.last_known_value + 10
                        self.target_process.write_bytes(self.counter_address, struct.pack('<I', new_value), 4)
                        
                        self.print_status(f"INTERCEPTED! {self.last_known_value} -> {new_value} (+10 instead of +1)", "exploit")
                        self.last_known_value = new_value
                    else:
                        self.last_known_value = current_value
                else:
                    self.last_known_value = current_value
                    
            except Exception as e:
                self.print_status(f"Monitor error: {e}", "debug")
                
            time.sleep(0.01)  # 100 FPS monitoring for maximum responsiveness
    
    def run(self):
        """Main exploit function"""
        print(f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                   FINAL COUNTER EXPLOIT                     ║
║                 Guaranteed Working Version                  ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}

{Fore.YELLOW}This exploit will successfully modify counter increments in real-time!{Style.RESET_ALL}
""")
        
        # Find and attach to target
        if not self.find_target_process():
            self.print_status("No Python processes found to attach to", "error")
            return
        
        # Scan for potential counter addresses
        candidates = self.scan_for_small_values()
        if not candidates:
            self.print_status("No potential counter addresses found", "error")
            return
        
        # Interactive counter detection
        self.counter_address = self.interactive_counter_detection(candidates)
        if not self.counter_address:
            self.print_status("Could not identify counter address", "error")
            self.print_status("Try running the exploit again and click the button during monitoring", "warning")
            return
        
        # Setup hotkeys
        self.setup_hotkeys()
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_and_intercept, daemon=True)
        monitor_thread.start()
        
        self.print_status("Exploit is ready!", "success")
        self.print_status("Press F1 to enable counter interception", "warning")
        self.print_status("Then click the target app button to see the exploit work!", "warning")
        print()
        
        # Main loop
        try:
            while self.running:
                time.sleep(1.0)
        except KeyboardInterrupt:
            self.print_status("Interrupted", "info")
        finally:
            self.monitoring_active = False
            if self.target_process:
                try:
                    self.target_process.close_handle()
                except:
                    pass
            self.print_status("Exploit terminated", "info")


if __name__ == "__main__":
    exploit = FinalCounterExploit()
    exploit.run()
